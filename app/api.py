from fastapi import APIRouter, HTTPException
from typing import List, Optional
from app.db.session_manager import session_manager
from app.models.qa_session import QASessionResponse, QAEntryResponse, QASessionWithEntries

router = APIRouter()


@router.get("/sessions", response_model=List[QASessionResponse])
async def get_sessions(limit: int = 10):
    """Get recent sessions"""
    try:
        sessions = session_manager.get_recent_sessions(limit)
        return sessions
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions/{session_id}", response_model=QASessionWithEntries)
async def get_session(session_id: int):
    """Get session with entries"""
    try:
        session = session_manager.get_session_with_entries(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        return session
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/current-entry", response_model=Optional[QAEntryResponse])
async def get_current_entry():
    """Get current entry"""
    try:
        entry = session_manager.get_current_entry()
        return entry
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
