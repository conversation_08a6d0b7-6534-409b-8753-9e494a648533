#!/usr/bin/env python3
"""
QA Agent System Tray Application

A system tray application that provides easy access to the QA Agent functionality.
"""

import logging
import threading
import sys
from typing import Optional
import tkinter as tk
from tkinter import messagebox, simpledialog

from app.system_app import QASystemApp
from app.core.app_config import config_manager
from app.db.session_manager import session_manager

logger = logging.getLogger(__name__)


class TrayApp:
    """System tray application for QA Agent"""
    
    def __init__(self):
        self.system_app: Optional[QASystemApp] = None
        self.app_thread: Optional[threading.Thread] = None
        self.tray_icon = None
        self.running = False
        
        # Try to import pystray for system tray
        try:
            import pystray
            from PIL import Image
            self.pystray = pystray
            self.PIL_Image = Image
            self.tray_available = True
        except ImportError:
            logger.warning("pystray or PIL not available. System tray will not be available.")
            self.tray_available = False
    
    def create_tray_icon(self):
        """Create the system tray icon"""
        if not self.tray_available:
            return None
        
        try:
            # Create a simple icon (you can replace this with a proper icon file)
            image = self.PIL_Image.new('RGB', (64, 64), color='blue')
            
            # Create menu items
            menu = self.pystray.Menu(
                self.pystray.MenuItem("QA Agent", None, enabled=False),
                self.pystray.Menu.SEPARATOR,
                self.pystray.MenuItem("Start Service", self.start_service, enabled=not self.is_service_running()),
                self.pystray.MenuItem("Stop Service", self.stop_service, enabled=self.is_service_running()),
                self.pystray.Menu.SEPARATOR,
                self.pystray.MenuItem("View Current Entry", self.view_current_entry),
                self.pystray.MenuItem("View Recent Sessions", self.view_recent_sessions),
                self.pystray.Menu.SEPARATOR,
                self.pystray.MenuItem("Settings", self.show_settings),
                self.pystray.MenuItem("About", self.show_about),
                self.pystray.Menu.SEPARATOR,
                self.pystray.MenuItem("Exit", self.quit_application)
            )
            
            # Create tray icon
            self.tray_icon = self.pystray.Icon(
                "qa_agent",
                image,
                "QA Agent",
                menu
            )
            
            return self.tray_icon
            
        except Exception as e:
            logger.error(f"Error creating tray icon: {e}")
            return None
    
    def is_service_running(self) -> bool:
        """Check if the QA service is running"""
        return self.system_app is not None and self.system_app.running
    
    def start_service(self, icon=None, item=None):
        """Start the QA service"""
        try:
            if self.is_service_running():
                messagebox.showinfo("QA Agent", "Service is already running")
                return
            
            self.system_app = QASystemApp()
            
            def run_app():
                self.system_app.run()
            
            self.app_thread = threading.Thread(target=run_app, daemon=True)
            self.app_thread.start()
            
            logger.info("QA service started from tray")
            
            if self.tray_available:
                # Update menu
                self.update_tray_menu()
            else:
                messagebox.showinfo("QA Agent", "Service started successfully")
                
        except Exception as e:
            logger.error(f"Error starting service: {e}")
            messagebox.showerror("Error", f"Failed to start service: {str(e)}")
    
    def stop_service(self, icon=None, item=None):
        """Stop the QA service"""
        try:
            if not self.is_service_running():
                messagebox.showinfo("QA Agent", "Service is not running")
                return
            
            self.system_app.stop()
            self.system_app = None
            
            logger.info("QA service stopped from tray")
            
            if self.tray_available:
                # Update menu
                self.update_tray_menu()
            else:
                messagebox.showinfo("QA Agent", "Service stopped successfully")
                
        except Exception as e:
            logger.error(f"Error stopping service: {e}")
            messagebox.showerror("Error", f"Failed to stop service: {str(e)}")
    
    def update_tray_menu(self):
        """Update the tray menu based on current state"""
        if not self.tray_available or not self.tray_icon:
            return
        
        try:
            # Create updated menu
            menu = self.pystray.Menu(
                self.pystray.MenuItem("QA Agent", None, enabled=False),
                self.pystray.Menu.SEPARATOR,
                self.pystray.MenuItem("Start Service", self.start_service, enabled=not self.is_service_running()),
                self.pystray.MenuItem("Stop Service", self.stop_service, enabled=self.is_service_running()),
                self.pystray.Menu.SEPARATOR,
                self.pystray.MenuItem("View Current Entry", self.view_current_entry),
                self.pystray.MenuItem("View Recent Sessions", self.view_recent_sessions),
                self.pystray.Menu.SEPARATOR,
                self.pystray.MenuItem("Settings", self.show_settings),
                self.pystray.MenuItem("About", self.show_about),
                self.pystray.Menu.SEPARATOR,
                self.pystray.MenuItem("Exit", self.quit_application)
            )
            
            self.tray_icon.menu = menu
            
        except Exception as e:
            logger.error(f"Error updating tray menu: {e}")
    
    def view_current_entry(self, icon=None, item=None):
        """View current Q&A entry"""
        try:
            current_entry = session_manager.get_current_entry()
            
            if not current_entry:
                messagebox.showinfo("Current Entry", "No current entry found")
                return
            
            info = f"Entry ID: {current_entry.id}\n"
            info += f"Session ID: {current_entry.session_id}\n\n"
            
            if current_entry.question:
                info += f"Question:\n{current_entry.question[:200]}...\n\n"
            else:
                info += "Question: Not captured\n\n"
            
            if current_entry.solution:
                info += f"Solution:\n{current_entry.solution[:200]}...\n\n"
            else:
                info += "Solution: Not captured\n\n"
            
            if current_entry.agent_response:
                info += f"Agent Response:\n{current_entry.agent_response[:200]}...\n"
            else:
                info += "Agent Response: Not generated\n"
            
            messagebox.showinfo("Current Entry", info)
            
        except Exception as e:
            logger.error(f"Error viewing current entry: {e}")
            messagebox.showerror("Error", f"Failed to view current entry: {str(e)}")
    
    def view_recent_sessions(self, icon=None, item=None):
        """View recent sessions"""
        try:
            sessions = session_manager.get_recent_sessions(5)
            
            if not sessions:
                messagebox.showinfo("Recent Sessions", "No sessions found")
                return
            
            info = "Recent Sessions:\n\n"
            for session in sessions:
                info += f"Session {session.id}: {session.session_name or 'Unnamed'}\n"
                info += f"Created: {session.created_at.strftime('%Y-%m-%d %H:%M')}\n"
                info += f"App: {session.app_name or 'Unknown'}\n"
                info += f"Active: {'Yes' if session.is_active else 'No'}\n\n"
            
            messagebox.showinfo("Recent Sessions", info)
            
        except Exception as e:
            logger.error(f"Error viewing recent sessions: {e}")
            messagebox.showerror("Error", f"Failed to view recent sessions: {str(e)}")
    
    def show_settings(self, icon=None, item=None):
        """Show settings dialog"""
        try:
            config = config_manager.get_config()
            
            # Create a simple settings window
            root = tk.Tk()
            root.title("QA Agent Settings")
            root.geometry("400x300")
            
            # Show current hotkeys
            tk.Label(root, text="Current Hotkeys:", font=("Arial", 12, "bold")).pack(pady=10)
            
            for hotkey_id, hotkey_config in config.hotkeys.items():
                hotkey_text = f"{hotkey_config.description}: {'+'.join(hotkey_config.keys)}"
                tk.Label(root, text=hotkey_text).pack(pady=2)
            
            # Settings
            tk.Label(root, text="\nSettings:", font=("Arial", 12, "bold")).pack(pady=(20, 10))
            
            tk.Label(root, text=f"Show Notifications: {config.show_notifications}").pack(pady=2)
            tk.Label(root, text=f"Auto Analyze: {config.auto_analyze}").pack(pady=2)
            tk.Label(root, text=f"Data Directory: {config.data_dir}").pack(pady=2)
            
            tk.Button(root, text="Close", command=root.destroy).pack(pady=20)
            
            root.mainloop()
            
        except Exception as e:
            logger.error(f"Error showing settings: {e}")
            messagebox.showerror("Error", f"Failed to show settings: {str(e)}")
    
    def show_about(self, icon=None, item=None):
        """Show about dialog"""
        about_text = """QA Agent System Application

A system application that listens to configurable hotkeys to capture questions and solutions, then feeds them to an AI agent for analysis and improved solutions.

Features:
- Global hotkey listening
- Question and solution capture
- AI-powered analysis
- Session management
- Cross-platform support

Version: 1.0.0"""
        
        messagebox.showinfo("About QA Agent", about_text)
    
    def quit_application(self, icon=None, item=None):
        """Quit the application"""
        try:
            if self.is_service_running():
                self.stop_service()
            
            self.running = False
            
            if self.tray_icon:
                self.tray_icon.stop()
            
            logger.info("Tray application quit")
            
        except Exception as e:
            logger.error(f"Error quitting application: {e}")
    
    def run(self):
        """Run the tray application"""
        self.running = True
        
        if self.tray_available:
            # Run with system tray
            self.tray_icon = self.create_tray_icon()
            if self.tray_icon:
                logger.info("Starting QA Agent tray application")
                self.tray_icon.run()
            else:
                logger.error("Failed to create tray icon")
                self.run_fallback()
        else:
            # Run fallback GUI
            self.run_fallback()
    
    def run_fallback(self):
        """Run fallback GUI when system tray is not available"""
        logger.info("Running fallback GUI")
        
        root = tk.Tk()
        root.title("QA Agent")
        root.geometry("300x200")
        
        tk.Label(root, text="QA Agent", font=("Arial", 16, "bold")).pack(pady=20)
        
        tk.Button(root, text="Start Service", command=self.start_service).pack(pady=5)
        tk.Button(root, text="Stop Service", command=self.stop_service).pack(pady=5)
        tk.Button(root, text="View Current Entry", command=self.view_current_entry).pack(pady=5)
        tk.Button(root, text="Settings", command=self.show_settings).pack(pady=5)
        tk.Button(root, text="Exit", command=root.quit).pack(pady=20)
        
        root.mainloop()


def main():
    """Main entry point for tray application"""
    app = TrayApp()
    app.run()


if __name__ == "__main__":
    main()
