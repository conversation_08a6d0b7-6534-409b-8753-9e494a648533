#!/usr/bin/env python3
"""
QA Agent System Application

A system application that listens to configurable hotkeys to capture questions and solutions,
then feeds them to an AI agent for analysis and improved solutions.
"""

import logging
import signal
import sys
import threading
import time
from typing import Optional

from app.core.app_config import config_manager
from app.utils.hotkey_listener import hotkey_listener
from app.utils.clipboard_handler import clipboard_handler
from app.utils.window_detector import window_detector
from app.db.session_manager import session_manager
from app.core.agent import qa_agent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qa_agent.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class QASystemApp:
    """Main system application"""
    
    def __init__(self):
        self.config = config_manager.get_config()
        self.running = False
        self.notification_enabled = self.config.show_notifications
        
    def show_notification(self, title: str, message: str) -> None:
        """Show system notification"""
        if not self.notification_enabled:
            return
        
        try:
            # Try to show notification based on platform
            import platform
            system = platform.system()
            
            if system == "Darwin":  # macOS
                import subprocess
                subprocess.run([
                    "osascript", "-e",
                    f'display notification "{message}" with title "{title}"'
                ])
            elif system == "Windows":
                # Windows notification (requires plyer or win10toast)
                try:
                    from plyer import notification
                    notification.notify(
                        title=title,
                        message=message,
                        timeout=3
                    )
                except ImportError:
                    logger.warning("Notification library not available on Windows")
            elif system == "Linux":
                # Linux notification using notify-send
                import subprocess
                subprocess.run(["notify-send", title, message])
                
        except Exception as e:
            logger.error(f"Error showing notification: {e}")
    
    def capture_question_action(self) -> None:
        """Action to capture a question"""
        try:
            logger.info("Capturing question...")
            
            # Get current window context
            window_info = window_detector.get_active_window()
            
            # Copy selected text
            selected_text = clipboard_handler.copy_selected_text()
            
            if selected_text and selected_text.strip():
                # Save to database
                entry = session_manager.capture_question(selected_text.strip(), window_info)
                
                self.show_notification(
                    "Question Captured",
                    f"Captured question: {selected_text[:50]}..."
                )
                
                logger.info(f"Question captured successfully: {len(selected_text)} characters")
                
                # Auto-analyze if enabled and we have both question and solution
                if self.config.auto_analyze:
                    current_entry = session_manager.get_current_entry()
                    if current_entry and current_entry.question and current_entry.solution:
                        self.analyze_action()
                        
            else:
                self.show_notification(
                    "No Text Selected",
                    "Please select some text before using the capture question hotkey"
                )
                logger.warning("No text selected for question capture")
                
        except Exception as e:
            logger.error(f"Error capturing question: {e}")
            self.show_notification("Error", f"Failed to capture question: {str(e)}")
    
    def capture_solution_action(self) -> None:
        """Action to capture a solution"""
        try:
            logger.info("Capturing solution...")
            
            # Get current window context
            window_info = window_detector.get_active_window()
            
            # Copy selected text
            selected_text = clipboard_handler.copy_selected_text()
            
            if selected_text and selected_text.strip():
                # Save to database
                entry = session_manager.capture_solution(selected_text.strip(), window_info)
                
                self.show_notification(
                    "Solution Captured",
                    f"Captured solution: {selected_text[:50]}..."
                )
                
                logger.info(f"Solution captured successfully: {len(selected_text)} characters")
                
                # Auto-analyze if enabled and we have both question and solution
                if self.config.auto_analyze:
                    current_entry = session_manager.get_current_entry()
                    if current_entry and current_entry.question and current_entry.solution:
                        self.analyze_action()
                        
            else:
                self.show_notification(
                    "No Text Selected",
                    "Please select some text before using the capture solution hotkey"
                )
                logger.warning("No text selected for solution capture")
                
        except Exception as e:
            logger.error(f"Error capturing solution: {e}")
            self.show_notification("Error", f"Failed to capture solution: {str(e)}")
    
    def analyze_action(self) -> None:
        """Action to analyze current question and solution"""
        try:
            logger.info("Analyzing current entry...")
            
            self.show_notification(
                "Analysis Started",
                "AI agent is analyzing your question and solution..."
            )
            
            # Run analysis in a separate thread to avoid blocking
            def run_analysis():
                try:
                    response = qa_agent.analyze_current_entry()
                    
                    if response:
                        self.show_notification(
                            "Analysis Complete",
                            "AI analysis completed. Check the logs or GUI for results."
                        )
                        logger.info("Analysis completed successfully")
                        
                        # Optionally copy the response to clipboard
                        clipboard_handler.set_clipboard_content(response)
                        
                    else:
                        self.show_notification(
                            "Analysis Failed",
                            "No current entry to analyze or missing question"
                        )
                        
                except Exception as e:
                    logger.error(f"Error in analysis thread: {e}")
                    self.show_notification("Analysis Error", f"Analysis failed: {str(e)}")
            
            analysis_thread = threading.Thread(target=run_analysis, daemon=True)
            analysis_thread.start()
            
        except Exception as e:
            logger.error(f"Error starting analysis: {e}")
            self.show_notification("Error", f"Failed to start analysis: {str(e)}")
    
    def register_hotkeys(self) -> bool:
        """Register all configured hotkeys"""
        try:
            # Map actions to methods
            action_map = {
                "capture_question": self.capture_question_action,
                "capture_solution": self.capture_solution_action,
                "analyze": self.analyze_action
            }
            
            # Register each hotkey
            for hotkey_id, hotkey_config in self.config.hotkeys.items():
                if hotkey_config.action in action_map:
                    success = hotkey_listener.register_hotkey(
                        hotkey_id,
                        hotkey_config.keys,
                        action_map[hotkey_config.action]
                    )
                    
                    if success:
                        logger.info(f"Registered hotkey {hotkey_id}: {'+'.join(hotkey_config.keys)}")
                    else:
                        logger.error(f"Failed to register hotkey {hotkey_id}")
                        return False
                else:
                    logger.warning(f"Unknown action for hotkey {hotkey_id}: {hotkey_config.action}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error registering hotkeys: {e}")
            return False
    
    def start(self) -> bool:
        """Start the system application"""
        try:
            logger.info("Starting QA Agent System Application...")
            
            # Load configuration
            self.config = config_manager.load_config()
            logger.info("Configuration loaded")
            
            # Register hotkeys
            if not self.register_hotkeys():
                logger.error("Failed to register hotkeys")
                return False
            
            # Start hotkey listener
            if not hotkey_listener.start_listening():
                logger.error("Failed to start hotkey listener")
                return False
            
            self.running = True
            
            self.show_notification(
                "QA Agent Started",
                "System is ready. Use configured hotkeys to capture questions and solutions."
            )
            
            logger.info("QA Agent System Application started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting application: {e}")
            return False
    
    def stop(self) -> None:
        """Stop the system application"""
        logger.info("Stopping QA Agent System Application...")
        
        self.running = False
        
        # Stop hotkey listener
        hotkey_listener.stop_listening()
        
        # Close current session
        session_manager.close_session()
        
        self.show_notification(
            "QA Agent Stopped",
            "System application has been stopped."
        )
        
        logger.info("QA Agent System Application stopped")
    
    def run(self) -> None:
        """Run the application main loop"""
        if not self.start():
            logger.error("Failed to start application")
            return
        
        # Set up signal handlers for graceful shutdown
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # Main loop
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received")
        finally:
            self.stop()


def main():
    """Main entry point"""
    app = QASystemApp()
    app.run()


if __name__ == "__main__":
    main()
