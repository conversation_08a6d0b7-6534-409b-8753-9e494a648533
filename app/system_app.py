#!/usr/bin/env python3
"""
QA Agent System Application

A system application that listens to configurable hotkeys to capture questions and solutions,
then feeds them to an AI agent for analysis and improved solutions.
"""

import logging
import signal
import sys
import threading
import time
from typing import Optional

from app.core.app_config import config_manager
from app.utils.hotkey_listener import hotkey_listener
from app.utils.clipboard_handler import clipboard_handler
from app.utils.window_detector import window_detector
from app.db.session_manager import session_manager
from app.core.agent import dsa_agent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qa_agent.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class QASystemApp:
    """Main system application"""
    
    def __init__(self):
        self.config = config_manager.get_config()
        self.running = False
        self.notification_enabled = self.config.show_notifications
        
    def show_notification(self, title: str, message: str) -> None:
        """Show system notification"""
        if not self.notification_enabled:
            return
        
        try:
            # Try to show notification based on platform
            import platform
            system = platform.system()
            
            if system == "Darwin":  # macOS
                import subprocess
                subprocess.run([
                    "osascript", "-e",
                    f'display notification "{message}" with title "{title}"'
                ])
            elif system == "Windows":
                # Windows notification (requires plyer or win10toast)
                try:
                    from plyer import notification
                    notification.notify(
                        title=title,
                        message=message,
                        timeout=3
                    )
                except ImportError:
                    logger.warning("Notification library not available on Windows")
            elif system == "Linux":
                # Linux notification using notify-send
                import subprocess
                subprocess.run(["notify-send", title, message])
                
        except Exception as e:
            logger.error(f"Error showing notification: {e}")
    
    def store_dsa_question_action(self) -> None:
        """Hotkey 1: Store DSA question and analyze it"""
        try:
            logger.info("Storing DSA question...")

            # Get current window context
            window_info = window_detector.get_active_window()

            # Copy selected text
            selected_text = clipboard_handler.copy_selected_text()

            if selected_text and selected_text.strip():
                self.show_notification(
                    "Analyzing DSA Question",
                    "AI is analyzing the DSA question..."
                )

                # Run analysis in a separate thread
                def analyze_question():
                    try:
                        response = dsa_agent.store_dsa_question(selected_text.strip(), window_info)

                        self.show_notification(
                            "DSA Question Stored",
                            "Question analyzed and stored successfully!"
                        )

                        # Copy analysis to clipboard
                        clipboard_handler.set_clipboard_content(response)
                        logger.info("DSA question stored and analyzed successfully")

                    except Exception as e:
                        logger.error(f"Error in DSA question analysis: {e}")
                        self.show_notification("Analysis Error", f"Failed to analyze question: {str(e)}")

                analysis_thread = threading.Thread(target=analyze_question, daemon=True)
                analysis_thread.start()

            else:
                self.show_notification(
                    "No Text Selected",
                    "Please select a DSA question before using this hotkey"
                )
                logger.warning("No text selected for DSA question capture")

        except Exception as e:
            logger.error(f"Error storing DSA question: {e}")
            self.show_notification("Error", f"Failed to store DSA question: {str(e)}")

    def get_hint_action(self) -> None:
        """Hotkey 2: Get progressive hints"""
        try:
            logger.info("Getting DSA hint...")

            self.show_notification(
                "Generating Hint",
                "AI is generating a helpful hint..."
            )

            # Run hint generation in a separate thread
            def generate_hint():
                try:
                    # Start with approach-level hint (can be made configurable)
                    from app.core.agent import DSAHintLevel
                    response = dsa_agent.get_progressive_hint(DSAHintLevel.APPROACH)

                    self.show_notification(
                        "Hint Ready",
                        "DSA hint generated successfully!"
                    )

                    # Copy hint to clipboard
                    clipboard_handler.set_clipboard_content(response)
                    logger.info("DSA hint generated successfully")

                except Exception as e:
                    logger.error(f"Error generating hint: {e}")
                    self.show_notification("Hint Error", f"Failed to generate hint: {str(e)}")

            hint_thread = threading.Thread(target=generate_hint, daemon=True)
            hint_thread.start()

        except Exception as e:
            logger.error(f"Error getting hint: {e}")
            self.show_notification("Error", f"Failed to get hint: {str(e)}")

    def get_full_solution_action(self) -> None:
        """Hotkey 3: Get full solution"""
        try:
            logger.info("Getting full DSA solution...")

            self.show_notification(
                "Generating Solution",
                "AI is generating the complete solution..."
            )

            # Run solution generation in a separate thread
            def generate_solution():
                try:
                    response = dsa_agent.get_full_solution()

                    self.show_notification(
                        "Solution Ready",
                        "Complete DSA solution generated!"
                    )

                    # Copy solution to clipboard
                    clipboard_handler.set_clipboard_content(response)
                    logger.info("Full DSA solution generated successfully")

                except Exception as e:
                    logger.error(f"Error generating solution: {e}")
                    self.show_notification("Solution Error", f"Failed to generate solution: {str(e)}")

            solution_thread = threading.Thread(target=generate_solution, daemon=True)
            solution_thread.start()

        except Exception as e:
            logger.error(f"Error getting solution: {e}")
            self.show_notification("Error", f"Failed to get solution: {str(e)}")

    def get_interview_solution_action(self) -> None:
        """Hotkey 4: Get interview-ready solution"""
        try:
            logger.info("Getting interview-ready DSA solution...")

            self.show_notification(
                "Generating Interview Solution",
                "AI is preparing interview-ready solution..."
            )

            # Run interview solution generation in a separate thread
            def generate_interview_solution():
                try:
                    response = dsa_agent.get_interview_ready_solution()

                    self.show_notification(
                        "Interview Solution Ready",
                        "Interview-ready solution generated!"
                    )

                    # Copy solution to clipboard
                    clipboard_handler.set_clipboard_content(response)
                    logger.info("Interview-ready DSA solution generated successfully")

                except Exception as e:
                    logger.error(f"Error generating interview solution: {e}")
                    self.show_notification("Interview Solution Error", f"Failed to generate interview solution: {str(e)}")

            interview_thread = threading.Thread(target=generate_interview_solution, daemon=True)
            interview_thread.start()

        except Exception as e:
            logger.error(f"Error getting interview solution: {e}")
            self.show_notification("Error", f"Failed to get interview solution: {str(e)}")

    def review_code_action(self) -> None:
        """Hotkey 5: Review user's code"""
        try:
            logger.info("Reviewing user code...")

            # Get current window context
            window_info = window_detector.get_active_window()

            # Copy selected text (user's code)
            selected_text = clipboard_handler.copy_selected_text()

            if selected_text and selected_text.strip():
                self.show_notification(
                    "Reviewing Code",
                    "AI is reviewing your code..."
                )

                # Run code review in a separate thread
                def review_code():
                    try:
                        response = dsa_agent.review_user_code(selected_text.strip())

                        self.show_notification(
                            "Code Review Complete",
                            "Code review completed successfully!"
                        )

                        # Copy review to clipboard
                        clipboard_handler.set_clipboard_content(response)
                        logger.info("Code review completed successfully")

                    except Exception as e:
                        logger.error(f"Error in code review: {e}")
                        self.show_notification("Review Error", f"Failed to review code: {str(e)}")

                review_thread = threading.Thread(target=review_code, daemon=True)
                review_thread.start()

            else:
                self.show_notification(
                    "No Code Selected",
                    "Please select your code before using this hotkey"
                )
                logger.warning("No code selected for review")

        except Exception as e:
            logger.error(f"Error reviewing code: {e}")
            self.show_notification("Error", f"Failed to review code: {str(e)}")
    

    
    def register_hotkeys(self) -> bool:
        """Register all configured hotkeys"""
        try:
            # Map actions to DSA-specific methods
            action_map = {
                "store_dsa_question": self.store_dsa_question_action,
                "get_hint": self.get_hint_action,
                "get_full_solution": self.get_full_solution_action,
                "get_interview_solution": self.get_interview_solution_action,
                "review_code": self.review_code_action,
                # Backward compatibility
                "capture_question": self.store_dsa_question_action,
                "capture_solution": self.review_code_action,
                "analyze": self.get_full_solution_action
            }
            
            # Register each hotkey
            for hotkey_id, hotkey_config in self.config.hotkeys.items():
                if hotkey_config.action in action_map:
                    success = hotkey_listener.register_hotkey(
                        hotkey_id,
                        hotkey_config.keys,
                        action_map[hotkey_config.action]
                    )
                    
                    if success:
                        logger.info(f"Registered hotkey {hotkey_id}: {'+'.join(hotkey_config.keys)}")
                    else:
                        logger.error(f"Failed to register hotkey {hotkey_id}")
                        return False
                else:
                    logger.warning(f"Unknown action for hotkey {hotkey_id}: {hotkey_config.action}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error registering hotkeys: {e}")
            return False
    
    def start(self) -> bool:
        """Start the system application"""
        try:
            logger.info("Starting QA Agent System Application...")
            
            # Load configuration
            self.config = config_manager.load_config()
            logger.info("Configuration loaded")
            
            # Register hotkeys
            if not self.register_hotkeys():
                logger.error("Failed to register hotkeys")
                return False
            
            # Start hotkey listener
            if not hotkey_listener.start_listening():
                logger.error("Failed to start hotkey listener")
                return False
            
            self.running = True
            
            self.show_notification(
                "QA Agent Started",
                "System is ready. Use configured hotkeys to capture questions and solutions."
            )
            
            logger.info("QA Agent System Application started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting application: {e}")
            return False
    
    def stop(self) -> None:
        """Stop the system application"""
        logger.info("Stopping QA Agent System Application...")
        
        self.running = False
        
        # Stop hotkey listener
        hotkey_listener.stop_listening()
        
        # Close current session
        session_manager.close_session()
        
        self.show_notification(
            "QA Agent Stopped",
            "System application has been stopped."
        )
        
        logger.info("QA Agent System Application stopped")
    
    def run(self) -> None:
        """Run the application main loop"""
        if not self.start():
            logger.error("Failed to start application")
            return
        
        # Set up signal handlers for graceful shutdown
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # Main loop
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received")
        finally:
            self.stop()


def main():
    """Main entry point"""
    app = QASystemApp()
    app.run()


if __name__ == "__main__":
    main()
