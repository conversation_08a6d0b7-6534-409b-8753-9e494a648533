from typing import Any, Dict, Optional
from pydantic import PostgresDsn, validator
from pydantic_settings import BaseSettings

class settings(BaseSettings):
    ENV: str = "dev"
    APP_NAME: str = "agent"

    DB_HOST: str
    DB_PORT: str
    DB_PASSWORD: str
    DB_NAME: str
    DB_USER: str
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        required = {"DB_USER", "DB_PASSWORD", "DB_HOST", "DB_PORT", "DB_NAME"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required database configuration: {missing}")

        return PostgresDsn.build(
            scheme="postgresql",
            username=values.get("DB_USER"),
            password=values.get("DB_PASSWORD"),
            host=values.get("DB_HOST"),
            port=int(values.get("DB_PORT", 5432)),
            path=f"{values.get('DB_NAME')}",
        )


    GEMINI_API_KEY: str

    class Config:
        env_file = ".env"
        extra = "allow"


setting = settings()