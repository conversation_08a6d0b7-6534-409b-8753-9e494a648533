"""
Selenium Fallback for ChatGPT Web Interface
Used when Gemini API key is not configured
"""

import logging
import time
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, WebDriverException

logger = logging.getLogger(__name__)


class ChatGPTSeleniumFallback:
    """Selenium automation for ChatGPT web interface"""
    
    def __init__(self):
        self.driver: Optional[webdriver.Chrome] = None
        self.is_logged_in = False
        self.wait_timeout = 30
        
    def _setup_driver(self) -> bool:
        """Setup Chrome driver with appropriate options"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            # Keep browser open for reuse
            chrome_options.add_experimental_option("detach", True)
            
            # Install and setup ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)
            
            logger.info("Chrome driver setup successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup Chrome driver: {e}")
            return False
    
    def initialize(self) -> bool:
        """Initialize the Selenium fallback system"""
        try:
            if not self._setup_driver():
                return False
            
            # Navigate to ChatGPT
            self.driver.get("https://chat.openai.com")
            
            # Wait for page to load
            WebDriverWait(self.driver, self.wait_timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            logger.info("ChatGPT page loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Selenium fallback: {e}")
            self.cleanup()
            return False
    
    def check_login_status(self) -> bool:
        """Check if user is logged into ChatGPT"""
        try:
            if not self.driver:
                return False
            
            # Look for login indicators
            # This is a simplified check - you might need to adjust selectors
            try:
                # Look for chat interface elements
                chat_input = self.driver.find_element(By.CSS_SELECTOR, "textarea[placeholder*='Message']")
                self.is_logged_in = True
                logger.info("User appears to be logged in to ChatGPT")
                return True
            except:
                # Look for login button
                login_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Log in') or contains(text(), 'Sign up')]")
                if login_elements:
                    self.is_logged_in = False
                    logger.info("User needs to log in to ChatGPT")
                    return False
                
            return False
            
        except Exception as e:
            logger.error(f"Error checking login status: {e}")
            return False
    
    def wait_for_login(self, timeout: int = 300) -> bool:
        """Wait for user to manually log in"""
        try:
            logger.info("Waiting for user to log in to ChatGPT...")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                if self.check_login_status():
                    logger.info("User successfully logged in")
                    return True
                time.sleep(2)
            
            logger.warning("Login timeout reached")
            return False
            
        except Exception as e:
            logger.error(f"Error waiting for login: {e}")
            return False
    
    def send_message(self, message: str) -> Optional[str]:
        """Send message to ChatGPT and get response"""
        try:
            if not self.driver or not self.is_logged_in:
                logger.error("Driver not initialized or user not logged in")
                return None
            
            # Find the chat input textarea
            chat_input = WebDriverWait(self.driver, self.wait_timeout).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "textarea[placeholder*='Message']"))
            )
            
            # Clear and type the message
            chat_input.clear()
            chat_input.send_keys(message)
            
            # Find and click send button
            send_button = self.driver.find_element(By.CSS_SELECTOR, "button[data-testid='send-button']")
            send_button.click()
            
            # Wait for response
            response = self._wait_for_response()
            return response
            
        except TimeoutException:
            logger.error("Timeout waiting for ChatGPT interface elements")
            return None
        except Exception as e:
            logger.error(f"Error sending message to ChatGPT: {e}")
            return None
    
    def _wait_for_response(self, timeout: int = 60) -> Optional[str]:
        """Wait for ChatGPT response"""
        try:
            # Wait for the response to appear
            # This is a simplified implementation - you might need to adjust selectors
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                try:
                    # Look for the latest response
                    response_elements = self.driver.find_elements(
                        By.CSS_SELECTOR, 
                        "[data-message-author-role='assistant'] .markdown"
                    )
                    
                    if response_elements:
                        latest_response = response_elements[-1]
                        response_text = latest_response.text
                        
                        if response_text and not response_text.endswith("..."):
                            logger.info("Received response from ChatGPT")
                            return response_text
                    
                    time.sleep(1)
                    
                except Exception:
                    time.sleep(1)
                    continue
            
            logger.warning("Timeout waiting for ChatGPT response")
            return None
            
        except Exception as e:
            logger.error(f"Error waiting for response: {e}")
            return None
    
    def send_dsa_question(self, question: str) -> Optional[str]:
        """Send DSA question with appropriate context"""
        dsa_prompt = f"""
        You are a DSA (Data Structures and Algorithms) expert assistant. Analyze this DSA question:

        Question: {question}

        Please provide:
        1. **Problem Type**: Identify the main DSA category (Array, String, Tree, Graph, DP, etc.)
        2. **Difficulty Level**: Easy/Medium/Hard
        3. **Key Concepts**: List the main algorithms/data structures needed
        4. **Similar Problems**: Mention 2-3 similar well-known problems
        5. **Initial Approach**: Brief high-level approach without giving away the solution

        Keep the response concise and educational.
        """
        
        return self.send_message(dsa_prompt)
    
    def send_hint_request(self, question: str, hint_level: str = "approach") -> Optional[str]:
        """Send hint request for DSA problem"""
        hint_prompts = {
            "approach": "Provide a HINT about the general approach to solve this DSA problem. DO NOT give the complete solution.",
            "algorithm": "Provide a more detailed HINT about the algorithm to use. DO NOT give the complete solution.",
            "implementation": "Provide HINTS about implementation details. DO NOT write the complete code.",
            "optimization": "Provide HINTS about optimizing the solution."
        }
        
        hint_prompt = f"""
        DSA Question: {question}
        
        {hint_prompts.get(hint_level, hint_prompts["approach"])}
        
        Focus on guidance without spoiling the learning experience.
        """
        
        return self.send_message(hint_prompt)
    
    def send_solution_request(self, question: str, solution_type: str = "full") -> Optional[str]:
        """Send solution request for DSA problem"""
        if solution_type == "interview":
            prompt = f"""
            DSA Question: {question}
            
            Provide an INTERVIEW-READY solution progression:
            
            1. BRUTE FORCE APPROACH with code
            2. OPTIMIZED APPROACH with better complexity
            3. INTERVIEW TIPS and common mistakes to avoid
            4. VARIATIONS and follow-up questions
            
            Format this as interview coaching.
            """
        else:
            prompt = f"""
            DSA Question: {question}
            
            Provide a COMPLETE solution with:
            1. Problem Analysis
            2. Approach explanation
            3. Complete working code
            4. Time/Space complexity
            5. Test cases
            6. Alternative approaches
            """
        
        return self.send_message(prompt)
    
    def send_code_review(self, question: str, user_code: str) -> Optional[str]:
        """Send code review request"""
        review_prompt = f"""
        DSA Question: {question}
        
        User's Code:
        ```
        {user_code}
        ```
        
        Please provide a comprehensive code review:
        1. Correctness analysis
        2. Code quality review
        3. Algorithm efficiency
        4. Specific fixes needed (point out exact issues)
        5. Best practices suggestions
        
        Focus on helping improve the existing code.
        """
        
        return self.send_message(review_prompt)
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                logger.info("Selenium driver cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# Global instance
selenium_fallback = ChatGPTSeleniumFallback()
