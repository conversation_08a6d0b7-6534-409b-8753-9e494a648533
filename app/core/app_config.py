import os
import yaml
from pathlib import Path
from typing import Dict, List, Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class HotkeyConfig(BaseModel):
    """Configuration for a single hotkey"""
    keys: List[str] = Field(..., description="List of keys for the hotkey combination")
    action: str = Field(..., description="Action to perform when hotkey is pressed")
    description: str = Field("", description="Human-readable description of the hotkey")


class AgentConfig(BaseModel):
    """Configuration for the AI agent"""
    model: str = Field("gpt-3.5-turbo", description="AI model to use")
    temperature: float = Field(0.7, description="Temperature for AI responses")
    max_tokens: int = Field(1000, description="Maximum tokens for AI responses")
    system_prompt: str = Field(
        "You are a helpful assistant that analyzes questions and solutions to provide better alternatives.",
        description="System prompt for the AI agent"
    )


class AppConfig(BaseSettings):
    """Main application configuration"""
    
    # Hotkey configurations
    hotkeys: Dict[str, HotkeyConfig] = Field(
        default_factory=lambda: {
            "capture_question": HotkeyConfig(
                keys=["ctrl", "shift", "q"],
                action="capture_question",
                description="Capture selected text as a question"
            ),
            "capture_solution": HotkeyConfig(
                keys=["ctrl", "shift", "s"],
                action="capture_solution",
                description="Capture selected text as a solution"
            ),
            "analyze": HotkeyConfig(
                keys=["ctrl", "shift", "a"],
                action="analyze",
                description="Analyze captured question and solution"
            )
        }
    )
    
    # Agent configuration
    agent: AgentConfig = Field(default_factory=AgentConfig)
    
    # Storage configuration
    data_dir: Path = Field(
        default_factory=lambda: Path.home() / ".qa_agent",
        description="Directory to store application data"
    )
    
    # UI configuration
    show_notifications: bool = Field(True, description="Show system notifications")
    auto_analyze: bool = Field(False, description="Automatically analyze when both question and solution are captured")
    
    class Config:
        env_prefix = "QA_AGENT_"
        case_sensitive = False


class ConfigManager:
    """Manages application configuration"""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.config_path = config_path or Path.home() / ".qa_agent" / "config.yaml"
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        self._config: Optional[AppConfig] = None
    
    def load_config(self) -> AppConfig:
        """Load configuration from file or create default"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    config_data = yaml.safe_load(f)
                self._config = AppConfig(**config_data)
            except Exception as e:
                print(f"Error loading config: {e}")
                self._config = AppConfig()
                self.save_config()
        else:
            self._config = AppConfig()
            self.save_config()
        
        return self._config
    
    def save_config(self) -> None:
        """Save current configuration to file"""
        if self._config is None:
            return
        
        config_dict = self._config.model_dump()
        
        # Convert Path objects to strings for YAML serialization
        if 'data_dir' in config_dict:
            config_dict['data_dir'] = str(config_dict['data_dir'])
        
        with open(self.config_path, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    def get_config(self) -> AppConfig:
        """Get current configuration"""
        if self._config is None:
            return self.load_config()
        return self._config
    
    def update_hotkey(self, hotkey_name: str, keys: List[str], description: str = "") -> None:
        """Update a hotkey configuration"""
        if self._config is None:
            self.load_config()
        
        self._config.hotkeys[hotkey_name] = HotkeyConfig(
            keys=keys,
            action=hotkey_name,
            description=description
        )
        self.save_config()
    
    def remove_hotkey(self, hotkey_name: str) -> None:
        """Remove a hotkey configuration"""
        if self._config is None:
            self.load_config()
        
        if hotkey_name in self._config.hotkeys:
            del self._config.hotkeys[hotkey_name]
            self.save_config()
    
    def update_agent_config(self, **kwargs) -> None:
        """Update agent configuration"""
        if self._config is None:
            self.load_config()
        
        for key, value in kwargs.items():
            if hasattr(self._config.agent, key):
                setattr(self._config.agent, key, value)
        
        self.save_config()


# Global config manager instance
config_manager = ConfigManager()
