"""
Memory Manager for DSA Assistant using mem0
Handles user personalization, learning history, and preferences
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class DSAMemoryManager:
    """Manages user memory and personalization using mem0"""
    
    def __init__(self):
        self.memory = None
        self._initialize_memory()
    
    def _initialize_memory(self):
        """Initialize mem0 memory system"""
        try:
            # Import mem0 when available
            # from mem0 import Memory
            # self.memory = Memory()
            
            # For now, use a simple in-memory storage as placeholder
            self.memory = {
                "users": {},
                "global_stats": {
                    "total_questions": 0,
                    "total_hints_requested": 0,
                    "total_solutions_generated": 0
                }
            }
            logger.info("Memory system initialized (placeholder mode)")
            
        except ImportError:
            logger.warning("mem0 not available, using placeholder memory system")
            self.memory = {
                "users": {},
                "global_stats": {
                    "total_questions": 0,
                    "total_hints_requested": 0,
                    "total_solutions_generated": 0
                }
            }
        except Exception as e:
            logger.error(f"Failed to initialize memory system: {e}")
            self.memory = None
    
    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get user profile and learning history"""
        if not self.memory:
            return self._default_user_profile()
        
        try:
            if user_id not in self.memory["users"]:
                self.memory["users"][user_id] = self._default_user_profile()
            
            return self.memory["users"][user_id]
            
        except Exception as e:
            logger.error(f"Error getting user profile: {e}")
            return self._default_user_profile()
    
    def _default_user_profile(self) -> Dict[str, Any]:
        """Default user profile structure"""
        return {
            "user_id": "default",
            "preferred_language": "python",
            "experience_level": "intermediate",
            "solved_problems": [],
            "struggled_topics": [],
            "hint_usage_patterns": {
                "approach_hints": 0,
                "algorithm_hints": 0,
                "implementation_hints": 0,
                "optimization_hints": 0
            },
            "problem_categories": {
                "arrays": {"solved": 0, "struggled": 0},
                "strings": {"solved": 0, "struggled": 0},
                "trees": {"solved": 0, "struggled": 0},
                "graphs": {"solved": 0, "struggled": 0},
                "dynamic_programming": {"solved": 0, "struggled": 0},
                "sorting": {"solved": 0, "struggled": 0},
                "searching": {"solved": 0, "struggled": 0},
                "linked_lists": {"solved": 0, "struggled": 0},
                "stacks_queues": {"solved": 0, "struggled": 0},
                "hash_tables": {"solved": 0, "struggled": 0}
            },
            "learning_preferences": {
                "prefers_step_by_step": True,
                "likes_multiple_approaches": True,
                "needs_complexity_analysis": True,
                "wants_interview_tips": True
            },
            "session_history": [],
            "created_at": datetime.utcnow().isoformat(),
            "last_active": datetime.utcnow().isoformat()
        }
    
    def update_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> None:
        """Update user preferences"""
        try:
            profile = self.get_user_profile(user_id)
            
            # Update basic preferences
            if "preferred_language" in preferences:
                profile["preferred_language"] = preferences["preferred_language"]
            if "experience_level" in preferences:
                profile["experience_level"] = preferences["experience_level"]
            
            # Update learning preferences
            if "learning_preferences" in preferences:
                profile["learning_preferences"].update(preferences["learning_preferences"])
            
            profile["last_active"] = datetime.utcnow().isoformat()
            
            if self.memory:
                self.memory["users"][user_id] = profile
            
            logger.info(f"Updated preferences for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error updating user preferences: {e}")
    
    def record_question_interaction(self, user_id: str, question: str, question_type: str, difficulty: str) -> None:
        """Record when user interacts with a question"""
        try:
            profile = self.get_user_profile(user_id)
            
            interaction = {
                "question": question[:100] + "..." if len(question) > 100 else question,
                "question_type": question_type.lower(),
                "difficulty": difficulty.lower(),
                "timestamp": datetime.utcnow().isoformat(),
                "action": "question_stored"
            }
            
            profile["session_history"].append(interaction)
            
            # Update category stats
            if question_type.lower() in profile["problem_categories"]:
                # We'll mark as "attempted" for now
                pass
            
            profile["last_active"] = datetime.utcnow().isoformat()
            
            if self.memory:
                self.memory["users"][user_id] = profile
                self.memory["global_stats"]["total_questions"] += 1
            
            logger.info(f"Recorded question interaction for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error recording question interaction: {e}")
    
    def record_hint_request(self, user_id: str, hint_type: str) -> None:
        """Record when user requests a hint"""
        try:
            profile = self.get_user_profile(user_id)
            
            # Update hint usage patterns
            hint_key = f"{hint_type.lower()}_hints"
            if hint_key in profile["hint_usage_patterns"]:
                profile["hint_usage_patterns"][hint_key] += 1
            
            interaction = {
                "action": "hint_requested",
                "hint_type": hint_type,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            profile["session_history"].append(interaction)
            profile["last_active"] = datetime.utcnow().isoformat()
            
            if self.memory:
                self.memory["users"][user_id] = profile
                self.memory["global_stats"]["total_hints_requested"] += 1
            
            logger.info(f"Recorded hint request for user {user_id}: {hint_type}")
            
        except Exception as e:
            logger.error(f"Error recording hint request: {e}")
    
    def record_solution_request(self, user_id: str, solution_type: str) -> None:
        """Record when user requests a solution"""
        try:
            profile = self.get_user_profile(user_id)
            
            interaction = {
                "action": "solution_requested",
                "solution_type": solution_type,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            profile["session_history"].append(interaction)
            profile["last_active"] = datetime.utcnow().isoformat()
            
            if self.memory:
                self.memory["users"][user_id] = profile
                self.memory["global_stats"]["total_solutions_generated"] += 1
            
            logger.info(f"Recorded solution request for user {user_id}: {solution_type}")
            
        except Exception as e:
            logger.error(f"Error recording solution request: {e}")
    
    def get_personalized_context(self, user_id: str) -> str:
        """Get personalized context for AI prompts"""
        try:
            profile = self.get_user_profile(user_id)
            
            # Build context based on user history and preferences
            context_parts = []
            
            # Experience level
            context_parts.append(f"User experience level: {profile['experience_level']}")
            
            # Language preference
            context_parts.append(f"Preferred programming language: {profile['preferred_language']}")
            
            # Hint usage patterns
            hint_patterns = profile["hint_usage_patterns"]
            most_used_hint = max(hint_patterns, key=hint_patterns.get) if any(hint_patterns.values()) else "approach"
            context_parts.append(f"Most requested hint type: {most_used_hint}")
            
            # Problem categories they've worked on
            categories_worked = [cat for cat, stats in profile["problem_categories"].items() 
                               if stats["solved"] > 0 or stats["struggled"] > 0]
            if categories_worked:
                context_parts.append(f"Has worked on: {', '.join(categories_worked[:3])}")
            
            # Learning preferences
            prefs = profile["learning_preferences"]
            if prefs["prefers_step_by_step"]:
                context_parts.append("Prefers step-by-step explanations")
            if prefs["likes_multiple_approaches"]:
                context_parts.append("Likes to see multiple solution approaches")
            if prefs["wants_interview_tips"]:
                context_parts.append("Values interview preparation tips")
            
            return ". ".join(context_parts) + "."
            
        except Exception as e:
            logger.error(f"Error getting personalized context: {e}")
            return f"User prefers {self.get_user_profile(user_id)['preferred_language']}. Learning DSA concepts."
    
    def mark_problem_solved(self, user_id: str, problem_category: str) -> None:
        """Mark a problem as solved in a category"""
        try:
            profile = self.get_user_profile(user_id)
            
            if problem_category.lower() in profile["problem_categories"]:
                profile["problem_categories"][problem_category.lower()]["solved"] += 1
            
            profile["last_active"] = datetime.utcnow().isoformat()
            
            if self.memory:
                self.memory["users"][user_id] = profile
            
            logger.info(f"Marked problem solved for user {user_id} in category {problem_category}")
            
        except Exception as e:
            logger.error(f"Error marking problem solved: {e}")
    
    def mark_problem_struggled(self, user_id: str, problem_category: str) -> None:
        """Mark a problem as struggled in a category"""
        try:
            profile = self.get_user_profile(user_id)
            
            if problem_category.lower() in profile["problem_categories"]:
                profile["problem_categories"][problem_category.lower()]["struggled"] += 1
            
            # Add to struggled topics if not already there
            if problem_category.lower() not in profile["struggled_topics"]:
                profile["struggled_topics"].append(problem_category.lower())
            
            profile["last_active"] = datetime.utcnow().isoformat()
            
            if self.memory:
                self.memory["users"][user_id] = profile
            
            logger.info(f"Marked problem struggled for user {user_id} in category {problem_category}")
            
        except Exception as e:
            logger.error(f"Error marking problem struggled: {e}")


# Global memory manager instance
memory_manager = DSAMemoryManager()
