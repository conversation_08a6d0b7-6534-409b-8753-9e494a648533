import logging
from typing import Optional, Dict, Any
from datetime import datetime

from langchain.schema import HumanMessage, SystemMessage
from langchain_google_genai import ChatGoogleGenerativeAI

from app.core.app_config import config_manager
from app.db.session_manager import session_manager
from app.models.qa_session import QAEntry

logger = logging.getLogger(__name__)


class QAAgent:
    """AI agent for analyzing questions and solutions"""
    
    def __init__(self):
        self.config = config_manager.get_config()
        self.llm = None
        self._initialize_llm()
    
    def _initialize_llm(self):
        """Initialize the language model"""
        try:
            # Use Google Gemini as configured in the original project
            from app.core.config import setting
            
            self.llm = ChatGoogleGenerativeAI(
                model="gemini-pro",
                google_api_key=setting.GEMINI_API_KEY,
                temperature=self.config.agent.temperature,
                max_output_tokens=self.config.agent.max_tokens
            )
            logger.info("Initialized Gemini LLM")
            
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            self.llm = None
    
    def analyze_question_solution(self, question: str, solution: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Analyze a question and solution pair to provide better alternatives"""
        if not self.llm:
            return "Error: AI agent not properly initialized"
        
        try:
            # Build context information
            context_info = ""
            if context:
                context_info = f"\nContext: The user was working in {context.get('app_name', 'unknown application')}"
                if context.get('title'):
                    context_info += f" with window title '{context['title']}'"
            
            # Create the prompt
            system_prompt = self.config.agent.system_prompt
            
            human_prompt = f"""
Please analyze the following question and solution pair and provide a better, more comprehensive solution.

Question:
{question}

Current Solution:
{solution}
{context_info}

Please provide:
1. An analysis of the current solution (strengths and weaknesses)
2. An improved solution with explanations
3. Additional tips or best practices related to this topic
4. Alternative approaches if applicable

Format your response clearly with sections for each part.
"""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=human_prompt)
            ]
            
            response = self.llm.invoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"Error analyzing question and solution: {e}")
            return f"Error analyzing content: {str(e)}"
    
    def analyze_question_only(self, question: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Analyze a question and provide guidance"""
        if not self.llm:
            return "Error: AI agent not properly initialized"
        
        try:
            # Build context information
            context_info = ""
            if context:
                context_info = f"\nContext: The user was working in {context.get('app_name', 'unknown application')}"
                if context.get('title'):
                    context_info += f" with window title '{context['title']}'"
            
            human_prompt = f"""
Please analyze the following question and provide helpful guidance.

Question:
{question}
{context_info}

Please provide:
1. A clear understanding of what the question is asking
2. Key concepts or topics involved
3. A comprehensive answer or solution approach
4. Best practices and tips related to this topic
5. Common pitfalls to avoid

Format your response clearly with sections for each part.
"""
            
            messages = [
                SystemMessage(content=self.config.agent.system_prompt),
                HumanMessage(content=human_prompt)
            ]
            
            response = self.llm.invoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"Error analyzing question: {e}")
            return f"Error analyzing question: {str(e)}"
    
    def analyze_current_entry(self) -> Optional[str]:
        """Analyze the current Q&A entry"""
        current_entry = session_manager.get_current_entry()
        
        if not current_entry:
            logger.warning("No current entry to analyze")
            return None
        
        if not current_entry.question:
            logger.warning("Current entry has no question")
            return None
        
        # Parse context if available
        context = None
        if current_entry.question_context:
            try:
                import json
                context = json.loads(current_entry.question_context)
            except Exception as e:
                logger.error(f"Error parsing question context: {e}")
        
        # Analyze based on what we have
        if current_entry.solution:
            # We have both question and solution
            response = self.analyze_question_solution(
                current_entry.question,
                current_entry.solution,
                context
            )
        else:
            # We only have a question
            response = self.analyze_question_only(
                current_entry.question,
                context
            )
        
        # Save the response
        session_manager.save_agent_response(response, current_entry.id)
        
        logger.info(f"Analyzed entry {current_entry.id}")
        return response
    
    def get_analysis_summary(self, entry: QAEntry) -> Dict[str, Any]:
        """Get a summary of an analysis"""
        return {
            "entry_id": entry.id,
            "has_question": bool(entry.question),
            "has_solution": bool(entry.solution),
            "has_agent_response": bool(entry.agent_response),
            "question_length": len(entry.question) if entry.question else 0,
            "solution_length": len(entry.solution) if entry.solution else 0,
            "response_length": len(entry.agent_response) if entry.agent_response else 0,
            "created_at": entry.created_at,
            "last_updated": entry.updated_at
        }


# Global agent instance
qa_agent = QAAgent()
