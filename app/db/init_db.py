from app.db.postgres import engine
from app.models.users import Base

def init_db():
    """
    Initialize the database by creating all tables.
    """
    # Import all models to ensure they are registered with Base
    import app.models.users

    print("Initializing database...")
    # creates all table
    Base.metadata.create_all(bind=engine)
    print("Database initialized successfully.")


if __name__ == "__main__":
    init_db()
