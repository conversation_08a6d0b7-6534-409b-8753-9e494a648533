import json
import logging
from datetime import datetime
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import create_engine, desc
from sqlalchemy.orm import sessionmaker

from app.models.qa_session import Base, QASession, QAEntry
from app.utils.window_detector import WindowInfo

logger = logging.getLogger(__name__)


class SessionManager:
    """Manages Q&A sessions and entries"""
    
    def __init__(self, database_url: str = "sqlite:///qa_agent.db"):
        self.engine = create_engine(database_url, echo=False)
        Base.metadata.create_all(bind=self.engine)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        self.current_session_id: Optional[int] = None
        self.current_entry_id: Optional[int] = None
    
    def get_db(self) -> Session:
        """Get database session"""
        return self.SessionLocal()
    
    def create_session(self, window_info: Optional[WindowInfo] = None, session_name: Optional[str] = None) -> QASession:
        """Create a new Q&A session"""
        with self.get_db() as db:
            session = QASession(
                session_name=session_name,
                window_title=window_info.title if window_info else None,
                app_name=window_info.app_name if window_info else None,
                process_name=window_info.process_name if window_info else None
            )
            db.add(session)
            db.commit()
            db.refresh(session)
            
            self.current_session_id = session.id
            logger.info(f"Created new session {session.id}")
            return session
    
    def get_or_create_current_session(self, window_info: Optional[WindowInfo] = None) -> QASession:
        """Get current active session or create a new one"""
        with self.get_db() as db:
            # Try to get the most recent active session
            if self.current_session_id:
                session = db.query(QASession).filter(
                    QASession.id == self.current_session_id,
                    QASession.is_active == True
                ).first()
                
                if session:
                    return session
            
            # Get the most recent active session
            session = db.query(QASession).filter(
                QASession.is_active == True
            ).order_by(desc(QASession.updated_at)).first()
            
            if session:
                self.current_session_id = session.id
                return session
            
            # Create a new session
            return self.create_session(window_info)
    
    def create_entry(self, session_id: Optional[int] = None) -> QAEntry:
        """Create a new Q&A entry"""
        if session_id is None:
            session_id = self.current_session_id
        
        if session_id is None:
            # Create a new session first
            session = self.create_session()
            session_id = session.id
        
        with self.get_db() as db:
            entry = QAEntry(session_id=session_id)
            db.add(entry)
            db.commit()
            db.refresh(entry)
            
            self.current_entry_id = entry.id
            logger.info(f"Created new entry {entry.id} in session {session_id}")
            return entry
    
    def get_or_create_current_entry(self) -> QAEntry:
        """Get current entry or create a new one"""
        with self.get_db() as db:
            # Try to get current entry
            if self.current_entry_id:
                entry = db.query(QAEntry).filter(QAEntry.id == self.current_entry_id).first()
                if entry:
                    return entry
            
            # Get the most recent entry from current session
            session = self.get_or_create_current_session()
            entry = db.query(QAEntry).filter(
                QAEntry.session_id == session.id
            ).order_by(desc(QAEntry.updated_at)).first()
            
            if entry and (entry.question is None or entry.solution is None):
                # Use existing incomplete entry
                self.current_entry_id = entry.id
                return entry
            
            # Create a new entry
            return self.create_entry(session.id)
    
    def capture_question(self, question: str, window_info: Optional[WindowInfo] = None) -> QAEntry:
        """Capture a question"""
        entry = self.get_or_create_current_entry()
        
        context_json = None
        if window_info:
            context_json = json.dumps({
                "title": window_info.title,
                "app_name": window_info.app_name,
                "process_name": window_info.process_name,
                "pid": window_info.pid,
                "bundle_id": window_info.bundle_id
            })
        
        with self.get_db() as db:
            entry = db.query(QAEntry).filter(QAEntry.id == entry.id).first()
            entry.question = question
            entry.question_captured_at = datetime.utcnow()
            entry.question_context = context_json
            db.commit()
            db.refresh(entry)
            
            logger.info(f"Captured question for entry {entry.id}")
            return entry
    
    def capture_solution(self, solution: str, window_info: Optional[WindowInfo] = None) -> QAEntry:
        """Capture a solution"""
        entry = self.get_or_create_current_entry()
        
        context_json = None
        if window_info:
            context_json = json.dumps({
                "title": window_info.title,
                "app_name": window_info.app_name,
                "process_name": window_info.process_name,
                "pid": window_info.pid,
                "bundle_id": window_info.bundle_id
            })
        
        with self.get_db() as db:
            entry = db.query(QAEntry).filter(QAEntry.id == entry.id).first()
            entry.solution = solution
            entry.solution_captured_at = datetime.utcnow()
            entry.solution_context = context_json
            db.commit()
            db.refresh(entry)
            
            logger.info(f"Captured solution for entry {entry.id}")
            return entry
    
    def save_agent_response(self, response: str, entry_id: Optional[int] = None) -> QAEntry:
        """Save agent response"""
        if entry_id is None:
            entry_id = self.current_entry_id
        
        if entry_id is None:
            raise ValueError("No current entry to save response to")
        
        with self.get_db() as db:
            entry = db.query(QAEntry).filter(QAEntry.id == entry_id).first()
            if not entry:
                raise ValueError(f"Entry {entry_id} not found")
            
            entry.agent_response = response
            entry.agent_response_at = datetime.utcnow()
            db.commit()
            db.refresh(entry)
            
            logger.info(f"Saved agent response for entry {entry.id}")
            return entry
    
    def get_session_with_entries(self, session_id: int) -> Optional[QASession]:
        """Get session with all its entries"""
        with self.get_db() as db:
            return db.query(QASession).filter(QASession.id == session_id).first()
    
    def get_recent_sessions(self, limit: int = 10) -> List[QASession]:
        """Get recent sessions"""
        with self.get_db() as db:
            return db.query(QASession).order_by(desc(QASession.updated_at)).limit(limit).all()
    
    def get_current_entry(self) -> Optional[QAEntry]:
        """Get current entry"""
        if self.current_entry_id is None:
            return None
        
        with self.get_db() as db:
            return db.query(QAEntry).filter(QAEntry.id == self.current_entry_id).first()
    
    def close_session(self, session_id: Optional[int] = None) -> None:
        """Close/deactivate a session"""
        if session_id is None:
            session_id = self.current_session_id
        
        if session_id is None:
            return
        
        with self.get_db() as db:
            session = db.query(QASession).filter(QASession.id == session_id).first()
            if session:
                session.is_active = False
                db.commit()
                logger.info(f"Closed session {session_id}")
        
        if session_id == self.current_session_id:
            self.current_session_id = None
            self.current_entry_id = None


# Global session manager instance
session_manager = SessionManager()
