import time
import threading
from typing import Optional, Callable
import logging
import subprocess
import platform

logger = logging.getLogger(__name__)


class ClipboardHandler:
    """Cross-platform clipboard management"""

    def __init__(self):
        self.system = platform.system()
        self._last_clipboard_content = ""
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._change_callbacks: list[Callable[[str], None]] = []

    def get_clipboard_content(self) -> Optional[str]:
        """Get current clipboard content"""
        try:
            # Try using pyperclip first
            try:
                import pyperclip
                return pyperclip.paste()
            except ImportError:
                pass

            # Fallback to system-specific commands
            if self.system == "Darwin":  # macOS
                result = subprocess.run(
                    ["pbpaste"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                return result.stdout if result.returncode == 0 else None

            elif self.system == "Windows":
                # Windows PowerShell command
                result = subprocess.run(
                    ["powershell", "-command", "Get-Clipboard"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                return result.stdout.strip() if result.returncode == 0 else None

            elif self.system == "Linux":
                # Try xclip first, then xsel
                for cmd in [["xclip", "-selection", "clipboard", "-o"], ["xsel", "--clipboard", "--output"]]:
                    try:
                        result = subprocess.run(
                            cmd,
                            capture_output=True,
                            text=True,
                            timeout=5
                        )
                        if result.returncode == 0:
                            return result.stdout
                    except FileNotFoundError:
                        continue

                logger.warning("No clipboard utility found (xclip or xsel)")
                return None

        except Exception as e:
            logger.error(f"Error getting clipboard content: {e}")
            return None

    def set_clipboard_content(self, content: str) -> bool:
        """Set clipboard content"""
        try:
            # Try using pyperclip first
            try:
                import pyperclip
                pyperclip.copy(content)
                return True
            except ImportError:
                pass

            # Fallback to system-specific commands
            if self.system == "Darwin":  # macOS
                process = subprocess.Popen(
                    ["pbcopy"],
                    stdin=subprocess.PIPE,
                    text=True
                )
                process.communicate(input=content)
                return process.returncode == 0

            elif self.system == "Windows":
                # Windows PowerShell command
                process = subprocess.Popen(
                    ["powershell", "-command", f"Set-Clipboard -Value '{content}'"],
                    stdin=subprocess.PIPE,
                    text=True
                )
                process.wait()
                return process.returncode == 0

            elif self.system == "Linux":
                # Try xclip first, then xsel
                for cmd in [["xclip", "-selection", "clipboard"], ["xsel", "--clipboard", "--input"]]:
                    try:
                        process = subprocess.Popen(
                            cmd,
                            stdin=subprocess.PIPE,
                            text=True
                        )
                        process.communicate(input=content)
                        if process.returncode == 0:
                            return True
                    except FileNotFoundError:
                        continue

                logger.warning("No clipboard utility found (xclip or xsel)")
                return False

        except Exception as e:
            logger.error(f"Error setting clipboard content: {e}")
            return False

    def copy_selected_text(self) -> Optional[str]:
        """
        Copy currently selected text to clipboard and return it.
        This simulates Ctrl+C to copy selected text.
        """
        try:
            # Store current clipboard content
            original_content = self.get_clipboard_content()

            # Simulate Ctrl+C to copy selected text
            if self.system == "Darwin":  # macOS
                subprocess.run(["osascript", "-e", 'tell application "System Events" to keystroke "c" using command down'])
            elif self.system == "Windows":
                # Use PowerShell to send Ctrl+C
                subprocess.run([
                    "powershell", "-command",
                    "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('^c')"
                ])
            elif self.system == "Linux":
                # Use xdotool if available
                try:
                    subprocess.run(["xdotool", "key", "ctrl+c"])
                except FileNotFoundError:
                    logger.warning("xdotool not found. Please install it for text selection copying.")
                    return None

            # Wait a bit for the clipboard to update
            time.sleep(0.1)

            # Get the new clipboard content
            new_content = self.get_clipboard_content()

            # If content changed, return the new content
            if new_content != original_content:
                return new_content
            else:
                logger.warning("No text appears to be selected")
                return None

        except Exception as e:
            logger.error(f"Error copying selected text: {e}")
            return None

    def add_change_callback(self, callback: Callable[[str], None]) -> None:
        """Add a callback to be called when clipboard content changes"""
        self._change_callbacks.append(callback)

    def remove_change_callback(self, callback: Callable[[str], None]) -> None:
        """Remove a clipboard change callback"""
        if callback in self._change_callbacks:
            self._change_callbacks.remove(callback)

    def start_monitoring(self, interval: float = 0.5) -> None:
        """Start monitoring clipboard for changes"""
        if self._monitoring:
            return

        self._monitoring = True
        self._last_clipboard_content = self.get_clipboard_content() or ""

        def monitor_loop():
            while self._monitoring:
                try:
                    current_content = self.get_clipboard_content() or ""
                    if current_content != self._last_clipboard_content:
                        self._last_clipboard_content = current_content
                        # Notify all callbacks
                        for callback in self._change_callbacks:
                            try:
                                callback(current_content)
                            except Exception as e:
                                logger.error(f"Error in clipboard change callback: {e}")

                    time.sleep(interval)
                except Exception as e:
                    logger.error(f"Error in clipboard monitoring: {e}")
                    time.sleep(interval)

        self._monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("Clipboard monitoring started")

    def stop_monitoring(self) -> None:
        """Stop monitoring clipboard for changes"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1)
        logger.info("Clipboard monitoring stopped")


# Global clipboard handler instance
clipboard_handler = ClipboardHandler()