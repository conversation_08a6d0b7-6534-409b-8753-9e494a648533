import threading
import time
from typing import Dict, Callable, Set, Optional
from collections import defaultdict
import logging

# For now, we'll create a simple implementation that can be extended
# when we add pynput dependency
logger = logging.getLogger(__name__)


class HotkeyListener:
    """Cross-platform hotkey listener using pynput"""

    def __init__(self):
        self.hotkeys: Dict[str, Dict] = {}
        self.callbacks: Dict[str, Callable] = {}
        self.running = False
        self.listener_thread: Optional[threading.Thread] = None
        self._pressed_keys: Set[str] = set()

    def register_hotkey(self, hotkey_id: str, keys: list, callback: Callable) -> bool:
        """Register a hotkey combination with a callback"""
        try:
            # Normalize key names
            normalized_keys = [self._normalize_key(key) for key in keys]

            self.hotkeys[hotkey_id] = {
                'keys': set(normalized_keys),
                'callback': callback
            }

            logger.info(f"Registered hotkey {hotkey_id}: {'+'.join(normalized_keys)}")
            return True

        except Exception as e:
            logger.error(f"Failed to register hotkey {hotkey_id}: {e}")
            return False

    def unregister_hotkey(self, hotkey_id: str) -> bool:
        """Unregister a hotkey"""
        if hotkey_id in self.hotkeys:
            del self.hotkeys[hotkey_id]
            logger.info(f"Unregistered hotkey {hotkey_id}")
            return True
        return False

    def _normalize_key(self, key: str) -> str:
        """Normalize key names for cross-platform compatibility"""
        key_mapping = {
            'ctrl': 'ctrl',
            'control': 'ctrl',
            'cmd': 'cmd',
            'command': 'cmd',
            'alt': 'alt',
            'option': 'alt',
            'shift': 'shift',
            'meta': 'cmd',  # Windows key on Windows, Cmd on Mac
        }

        normalized = key.lower().strip()
        return key_mapping.get(normalized, normalized)

    def _check_hotkey_match(self, pressed_keys: Set[str]) -> Optional[str]:
        """Check if current pressed keys match any registered hotkey"""
        for hotkey_id, hotkey_data in self.hotkeys.items():
            if hotkey_data['keys'] == pressed_keys:
                return hotkey_id
        return None

    def start_listening(self) -> bool:
        """Start the hotkey listener"""
        if self.running:
            logger.warning("Hotkey listener is already running")
            return False

        try:
            # Try to import pynput
            from pynput import keyboard

            self.running = True

            def on_press(key):
                try:
                    # Handle special keys
                    if hasattr(key, 'name'):
                        key_name = self._normalize_key(key.name)
                    elif hasattr(key, 'char') and key.char:
                        key_name = key.char.lower()
                    else:
                        return

                    self._pressed_keys.add(key_name)

                    # Check for hotkey matches
                    matched_hotkey = self._check_hotkey_match(self._pressed_keys)
                    if matched_hotkey:
                        callback = self.hotkeys[matched_hotkey]['callback']
                        # Run callback in a separate thread to avoid blocking
                        threading.Thread(target=callback, daemon=True).start()

                except Exception as e:
                    logger.error(f"Error in key press handler: {e}")

            def on_release(key):
                try:
                    # Handle special keys
                    if hasattr(key, 'name'):
                        key_name = self._normalize_key(key.name)
                    elif hasattr(key, 'char') and key.char:
                        key_name = key.char.lower()
                    else:
                        return

                    self._pressed_keys.discard(key_name)

                except Exception as e:
                    logger.error(f"Error in key release handler: {e}")

            # Start the keyboard listener
            self.listener = keyboard.Listener(
                on_press=on_press,
                on_release=on_release
            )

            self.listener.start()
            logger.info("Hotkey listener started successfully")
            return True

        except ImportError:
            logger.error("pynput library not found. Please install it: pip install pynput")
            return False
        except Exception as e:
            logger.error(f"Failed to start hotkey listener: {e}")
            self.running = False
            return False

    def stop_listening(self) -> None:
        """Stop the hotkey listener"""
        if not self.running:
            return

        self.running = False

        if hasattr(self, 'listener'):
            self.listener.stop()

        self._pressed_keys.clear()
        logger.info("Hotkey listener stopped")

    def is_running(self) -> bool:
        """Check if the listener is running"""
        return self.running

    def get_registered_hotkeys(self) -> Dict[str, list]:
        """Get all registered hotkeys"""
        return {
            hotkey_id: list(data['keys'])
            for hotkey_id, data in self.hotkeys.items()
        }


# Global hotkey listener instance
hotkey_listener = HotkeyListener()