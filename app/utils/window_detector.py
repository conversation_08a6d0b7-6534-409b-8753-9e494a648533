import platform
import subprocess
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class WindowInfo:
    """Information about the active window"""
    title: str
    app_name: str
    process_name: str
    pid: Optional[int] = None
    bundle_id: Optional[str] = None  # macOS specific


class WindowDetector:
    """Cross-platform window detection"""

    def __init__(self):
        self.system = platform.system()

    def get_active_window(self) -> Optional[WindowInfo]:
        """Get information about the currently active window"""
        try:
            if self.system == "Darwin":  # macOS
                return self._get_active_window_macos()
            elif self.system == "Windows":
                return self._get_active_window_windows()
            elif self.system == "Linux":
                return self._get_active_window_linux()
            else:
                logger.warning(f"Unsupported platform: {self.system}")
                return None
        except Exception as e:
            logger.error(f"Error getting active window: {e}")
            return None

    def _get_active_window_macos(self) -> Optional[WindowInfo]:
        """Get active window on macOS using AppleScript"""
        try:
            # Get frontmost application
            script = '''
            tell application "System Events"
                set frontApp to first application process whose frontmost is true
                set appName to name of frontApp
                set appPID to unix id of frontApp

                try
                    set windowTitle to name of front window of frontApp
                on error
                    set windowTitle to ""
                end try

                return appName & "|" & windowTitle & "|" & (appPID as string)
            end tell
            '''

            result = subprocess.run(
                ["osascript", "-e", script],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                parts = result.stdout.strip().split("|")
                if len(parts) >= 3:
                    app_name = parts[0]
                    window_title = parts[1]
                    pid = int(parts[2]) if parts[2].isdigit() else None

                    # Try to get bundle ID
                    bundle_id = self._get_bundle_id_macos(pid) if pid else None

                    return WindowInfo(
                        title=window_title,
                        app_name=app_name,
                        process_name=app_name,
                        pid=pid,
                        bundle_id=bundle_id
                    )

        except Exception as e:
            logger.error(f"Error getting macOS active window: {e}")

        return None

    def _get_bundle_id_macos(self, pid: int) -> Optional[str]:
        """Get bundle ID for a process on macOS"""
        try:
            result = subprocess.run(
                ["ps", "-p", str(pid), "-o", "comm="],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                return result.stdout.strip()
        except Exception as e:
            logger.error(f"Error getting bundle ID: {e}")

        return None

    def _get_active_window_windows(self) -> Optional[WindowInfo]:
        """Get active window on Windows using PowerShell"""
        try:
            script = '''
            Add-Type @"
                using System;
                using System.Runtime.InteropServices;
                using System.Text;
                public class Win32 {
                    [DllImport("user32.dll")]
                    public static extern IntPtr GetForegroundWindow();
                    [DllImport("user32.dll")]
                    public static extern int GetWindowText(IntPtr hWnd, StringBuilder text, int count);
                    [DllImport("user32.dll")]
                    public static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint processId);
                }
"@

            $hwnd = [Win32]::GetForegroundWindow()
            $title = New-Object System.Text.StringBuilder 256
            [Win32]::GetWindowText($hwnd, $title, $title.Capacity)

            $processId = 0
            [Win32]::GetWindowThreadProcessId($hwnd, [ref]$processId)

            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
            if ($process) {
                Write-Output "$($process.ProcessName)|$($title.ToString())|$($processId)"
            }
            '''

            result = subprocess.run(
                ["powershell", "-command", script],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0 and result.stdout.strip():
                parts = result.stdout.strip().split("|")
                if len(parts) >= 3:
                    process_name = parts[0]
                    window_title = parts[1]
                    pid = int(parts[2]) if parts[2].isdigit() else None

                    return WindowInfo(
                        title=window_title,
                        app_name=process_name,
                        process_name=process_name,
                        pid=pid
                    )

        except Exception as e:
            logger.error(f"Error getting Windows active window: {e}")

        return None

    def _get_active_window_linux(self) -> Optional[WindowInfo]:
        """Get active window on Linux using xdotool and xprop"""
        try:
            # Get active window ID
            result = subprocess.run(
                ["xdotool", "getactivewindow"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode != 0:
                logger.warning("xdotool not found or failed")
                return None

            window_id = result.stdout.strip()

            # Get window title
            title_result = subprocess.run(
                ["xdotool", "getwindowname", window_id],
                capture_output=True,
                text=True,
                timeout=5
            )

            window_title = title_result.stdout.strip() if title_result.returncode == 0 else ""

            # Get process info
            pid_result = subprocess.run(
                ["xdotool", "getwindowpid", window_id],
                capture_output=True,
                text=True,
                timeout=5
            )

            pid = None
            process_name = ""

            if pid_result.returncode == 0:
                pid = int(pid_result.stdout.strip())

                # Get process name
                ps_result = subprocess.run(
                    ["ps", "-p", str(pid), "-o", "comm="],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if ps_result.returncode == 0:
                    process_name = ps_result.stdout.strip()

            return WindowInfo(
                title=window_title,
                app_name=process_name,
                process_name=process_name,
                pid=pid
            )

        except FileNotFoundError:
            logger.warning("xdotool not found. Please install it: sudo apt-get install xdotool")
        except Exception as e:
            logger.error(f"Error getting Linux active window: {e}")

        return None

    def is_browser_window(self, window_info: WindowInfo) -> bool:
        """Check if the window is a web browser"""
        if not window_info:
            return False

        browser_processes = {
            'chrome', 'firefox', 'safari', 'edge', 'opera', 'brave',
            'chromium', 'vivaldi', 'arc', 'google chrome'
        }

        process_name = window_info.process_name.lower()
        app_name = window_info.app_name.lower()

        return any(browser in process_name or browser in app_name for browser in browser_processes)

    def is_code_editor(self, window_info: WindowInfo) -> bool:
        """Check if the window is a code editor"""
        if not window_info:
            return False

        editor_processes = {
            'code', 'vscode', 'sublime', 'atom', 'vim', 'emacs', 'notepad++',
            'pycharm', 'intellij', 'webstorm', 'phpstorm', 'clion',
            'visual studio code', 'sublime text'
        }

        process_name = window_info.process_name.lower()
        app_name = window_info.app_name.lower()

        return any(editor in process_name or editor in app_name for editor in editor_processes)


# Global window detector instance
window_detector = WindowDetector()