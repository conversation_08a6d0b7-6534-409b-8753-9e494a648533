from sqlalchemy import (
    Column, String, DateTime, Boolean, Enum as SQLAlchemyEnum
)
from datetime import datetime
import uuid
from enum import Enum
from sqlalchemy.orm import declarative_base

Base = declarative_base()

class Role(str, Enum):
    USER = "user"
    ADMIN = "admin"


class User(Base):
    __tablename__ = "user"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    email = Column(String, nullable=False, index=True, unique=True)
    hashed_password = Column(String, nullable=False)
    
    role = Column(SQLAlchemyEnum(Role), nullable=False, default=Role.USER)
    hashed_otp = Column(String, nullable=False)
    name = Column(String, nullable=False)
    is_email_verified = Column(Boolean, default=False)
    
    # Meta
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<Invite {self.email} - {self.status}>"
