from datetime import datetime
from typing import Optional, List
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Text, DateTime, <PERSON>olean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from pydantic import BaseModel

Base = declarative_base()


class QASession(Base):
    """Database model for Q&A sessions"""
    __tablename__ = "qa_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_name = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Context information
    window_title = Column(String(500), nullable=True)
    app_name = Column(String(255), nullable=True)
    process_name = Column(String(255), nullable=True)
    
    # Relationships
    entries = relationship("QAEntry", back_populates="session", cascade="all, delete-orphan")


class QAEntry(Base):
    """Database model for individual Q&A entries"""
    __tablename__ = "qa_entries"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("qa_sessions.id"), nullable=False)
    
    # Content
    question = Column(Text, nullable=True)
    solution = Column(Text, nullable=True)
    agent_response = Column(Text, nullable=True)
    
    # Metadata
    question_captured_at = Column(DateTime, nullable=True)
    solution_captured_at = Column(DateTime, nullable=True)
    agent_response_at = Column(DateTime, nullable=True)
    
    # Context when captured
    question_context = Column(Text, nullable=True)  # JSON string with window info
    solution_context = Column(Text, nullable=True)  # JSON string with window info
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    session = relationship("QASession", back_populates="entries")


# Pydantic models for API
class QASessionCreate(BaseModel):
    session_name: Optional[str] = None
    window_title: Optional[str] = None
    app_name: Optional[str] = None
    process_name: Optional[str] = None


class QASessionResponse(BaseModel):
    id: int
    session_name: Optional[str]
    created_at: datetime
    updated_at: datetime
    is_active: bool
    window_title: Optional[str]
    app_name: Optional[str]
    process_name: Optional[str]
    
    class Config:
        from_attributes = True


class QAEntryCreate(BaseModel):
    session_id: int
    question: Optional[str] = None
    solution: Optional[str] = None
    question_context: Optional[str] = None
    solution_context: Optional[str] = None


class QAEntryUpdate(BaseModel):
    question: Optional[str] = None
    solution: Optional[str] = None
    agent_response: Optional[str] = None
    question_context: Optional[str] = None
    solution_context: Optional[str] = None


class QAEntryResponse(BaseModel):
    id: int
    session_id: int
    question: Optional[str]
    solution: Optional[str]
    agent_response: Optional[str]
    question_captured_at: Optional[datetime]
    solution_captured_at: Optional[datetime]
    agent_response_at: Optional[datetime]
    question_context: Optional[str]
    solution_context: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class QASessionWithEntries(BaseModel):
    id: int
    session_name: Optional[str]
    created_at: datetime
    updated_at: datetime
    is_active: bool
    window_title: Optional[str]
    app_name: Optional[str]
    process_name: Optional[str]
    entries: List[QAEntryResponse]
    
    class Config:
        from_attributes = True
