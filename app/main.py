#!/usr/bin/env python3
"""
QA Agent Main Entry Point

This script can run the QA Agent in different modes:
- system: Run as a system application with hotkey listening
- tray: Run with system tray interface
- api: Run as a FastAPI web service (original mode)
"""

import sys
import argparse
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def run_system_app():
    """Run as system application"""
    from app.system_app import main
    main()


def run_tray_app():
    """Run with system tray"""
    from app.tray_app import main
    main()


def run_api_app():
    """Run as FastAPI application"""
    from fastapi import FastAPI
    import uvicorn

    app = FastAPI(title="QA Agent API")

    @app.get("/health")
    async def health_check():
        return {"status": "ok"}

    @app.get("/")
    async def root():
        return {"message": "QA Agent API", "version": "1.0.0"}

    @app.on_event("startup")
    async def startup():
        logger.info("QA Agent API started")

    # Add API endpoints for managing sessions and entries
    from app.api import router
    app.include_router(router, prefix="/api/v1")

    uvicorn.run(app, host="0.0.0.0", port=8000)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="QA Agent Application")
    parser.add_argument(
        "mode",
        choices=["system", "tray", "api"],
        nargs="?",
        default="tray",
        help="Run mode: system (background service), tray (system tray), or api (web service)"
    )

    args = parser.parse_args()

    logger.info(f"Starting QA Agent in {args.mode} mode")

    try:
        if args.mode == "system":
            run_system_app()
        elif args.mode == "tray":
            run_tray_app()
        elif args.mode == "api":
            run_api_app()
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
