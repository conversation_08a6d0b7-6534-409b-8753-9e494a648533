# QA Agent System Application

A system application that listens to configurable hotkeys to capture questions and solutions, then feeds them to an AI agent for analysis and improved solutions.

## Features

- **Global Hotkey Listening**: Capture questions and solutions from any application using configurable hotkeys
- **AI-Powered Analysis**: Uses Google Gemini to analyze questions and provide improved solutions
- **Session Management**: Organizes captured Q&A pairs into sessions with context information
- **Cross-Platform Support**: Works on macOS, Windows, and Linux
- **System Tray Integration**: Runs in the background with easy access through system tray
- **Context Awareness**: Captures window and application context when hotkeys are triggered
- **Flexible Configuration**: Customizable hotkeys and agent settings

## Installation

### Prerequisites

1. Python 3.11 or higher
2. Poetry (for dependency management)

### Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd agent
```

2. Install dependencies:
```bash
poetry install
```

3. Install additional system dependencies:

**macOS:**
- No additional dependencies required

**Windows:**
- Install PowerShell (usually pre-installed)

**Linux:**
- Install required packages:
```bash
sudo apt-get install xdotool xclip  # or xsel
```

4. Set up environment variables:
Create a `.env` file in the project root:
```env
GEMINI_API_KEY=your_google_gemini_api_key_here
```

## Usage

### Running the Application

The application can be run in three modes:

1. **System Tray Mode (Recommended)**:
```bash
python launch.py tray
```

2. **Background Service Mode**:
```bash
python launch.py system
```

3. **API Mode** (for web interface):
```bash
python launch.py api
```

### Default Hotkeys

- **Ctrl+Shift+Q**: Capture selected text as a question
- **Ctrl+Shift+S**: Capture selected text as a solution
- **Ctrl+Shift+A**: Analyze current question and solution

### How to Use

1. **Start the application** in tray mode
2. **Select text** in any application (browser, editor, etc.)
3. **Press Ctrl+Shift+Q** to capture it as a question
4. **Select another text** (your solution or attempt)
5. **Press Ctrl+Shift+S** to capture it as a solution
6. **Press Ctrl+Shift+A** to get AI analysis and improved solution
7. **Check the system tray** for notifications and access to results

### Configuration

The application creates a configuration file at `~/.qa_agent/config.yaml`. You can modify:

- Hotkey combinations
- AI agent settings (model, temperature, etc.)
- Notification preferences
- Auto-analysis settings

Example configuration:
```yaml
hotkeys:
  capture_question:
    keys: ["ctrl", "shift", "q"]
    action: "capture_question"
    description: "Capture selected text as a question"
  capture_solution:
    keys: ["ctrl", "shift", "s"]
    action: "capture_solution"
    description: "Capture selected text as a solution"
  analyze:
    keys: ["ctrl", "shift", "a"]
    action: "analyze"
    description: "Analyze captured question and solution"

agent:
  model: "gpt-3.5-turbo"
  temperature: 0.7
  max_tokens: 1000
  system_prompt: "You are a helpful assistant..."

show_notifications: true
auto_analyze: false
data_dir: "~/.qa_agent"
```

## Data Storage

The application stores data locally in:
- **Configuration**: `~/.qa_agent/config.yaml`
- **Database**: `qa_agent.db` (SQLite)
- **Logs**: `qa_agent.log`

## System Tray Features

When running in tray mode, you can:
- Start/stop the hotkey service
- View current Q&A entry
- View recent sessions
- Access settings
- View application information

## Troubleshooting

### Common Issues

1. **Hotkeys not working**:
   - Ensure the application is running with proper permissions
   - Check if other applications are using the same hotkey combinations
   - Try different hotkey combinations in the configuration

2. **Text not being captured**:
   - Make sure text is selected before pressing hotkeys
   - Check clipboard permissions on your system
   - Verify that clipboard utilities are installed (Linux)

3. **AI analysis not working**:
   - Verify your GEMINI_API_KEY is set correctly
   - Check internet connection
   - Review logs for error messages

4. **System tray not appearing**:
   - Install required dependencies: `pip install pystray pillow`
   - Use fallback GUI mode if tray is not supported

### Platform-Specific Notes

**macOS:**
- May require accessibility permissions for global hotkeys
- Go to System Preferences > Security & Privacy > Accessibility

**Windows:**
- May require running as administrator for global hotkeys
- Windows Defender might flag the application initially

**Linux:**
- Requires X11 (not Wayland) for hotkey detection
- Install xdotool and xclip/xsel packages

## Development

### Project Structure

```
app/
├── core/           # Core configuration and agent logic
├── db/             # Database models and session management
├── models/         # Pydantic models
├── utils/          # Utility modules (hotkeys, clipboard, window detection)
├── main.py         # Main entry point
├── system_app.py   # System application logic
├── tray_app.py     # System tray application
└── api.py          # API endpoints
```

### Running Tests

```bash
poetry run pytest
```

### Adding New Features

1. Update configuration models in `app/core/app_config.py`
2. Add new hotkey actions in `app/system_app.py`
3. Update the system tray menu in `app/tray_app.py`
4. Add API endpoints in `app/api.py` if needed

## License

This project is licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs in `qa_agent.log`
3. Create an issue on GitHub with detailed information
