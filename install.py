#!/usr/bin/env python3
"""
QA Agent Installation Script

This script helps install the QA Agent system application and its dependencies.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, check=True):
    """Run a shell command"""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 11):
        print("Error: Python 3.11 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"Python version OK: {version.major}.{version.minor}.{version.micro}")
    return True


def install_system_dependencies():
    """Install system-specific dependencies"""
    system = platform.system()
    
    print(f"Installing system dependencies for {system}...")
    
    if system == "Linux":
        print("Installing Linux dependencies...")
        commands = [
            "sudo apt-get update",
            "sudo apt-get install -y xdotool xclip python3-tk python3-dev"
        ]
        
        for cmd in commands:
            if not run_command(cmd, check=False):
                print(f"Warning: Failed to run {cmd}")
                print("You may need to install these packages manually:")
                print("- xdotool (for hotkey detection)")
                print("- xclip or xsel (for clipboard access)")
                print("- python3-tk (for GUI)")
    
    elif system == "Darwin":  # macOS
        print("macOS detected. Most dependencies should work out of the box.")
        print("You may need to grant accessibility permissions in System Preferences.")
    
    elif system == "Windows":
        print("Windows detected. Most dependencies should work out of the box.")
        print("You may need to run as administrator for global hotkeys.")
    
    else:
        print(f"Unsupported system: {system}")
        return False
    
    return True


def install_python_dependencies():
    """Install Python dependencies"""
    print("Installing Python dependencies...")
    
    # Try poetry first
    if run_command("poetry --version", check=False):
        print("Using Poetry for dependency management...")
        return run_command("poetry install")
    
    # Fallback to pip
    print("Using pip for dependency management...")
    return run_command("pip install -r requirements.txt")


def create_config_directory():
    """Create configuration directory"""
    config_dir = Path.home() / ".qa_agent"
    config_dir.mkdir(exist_ok=True)
    print(f"Created config directory: {config_dir}")
    return True


def create_desktop_shortcut():
    """Create desktop shortcut (optional)"""
    system = platform.system()
    
    if system == "Linux":
        desktop_file = Path.home() / "Desktop" / "QA-Agent.desktop"
        script_path = Path(__file__).parent.absolute() / "launch.py"
        
        desktop_content = f"""[Desktop Entry]
Name=QA Agent
Comment=AI-powered Q&A capture and analysis tool
Exec=python3 {script_path} tray
Icon=applications-development
Terminal=false
Type=Application
Categories=Development;Utility;
"""
        
        try:
            with open(desktop_file, 'w') as f:
                f.write(desktop_content)
            os.chmod(desktop_file, 0o755)
            print(f"Created desktop shortcut: {desktop_file}")
        except Exception as e:
            print(f"Failed to create desktop shortcut: {e}")
    
    elif system == "Darwin":  # macOS
        print("To create a macOS app, you can use py2app or create an Automator application.")
    
    elif system == "Windows":
        print("To create a Windows shortcut, you can create a .bat file or use pyinstaller.")


def setup_environment():
    """Setup environment file"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("Creating .env file...")
        env_content = """# QA Agent Configuration
# Add your Google Gemini API key here
GEMINI_API_KEY=your_api_key_here

# Database configuration (optional, defaults to SQLite)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=qa_agent
# DB_USER=qa_user
# DB_PASSWORD=your_password
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("Created .env file. Please edit it and add your GEMINI_API_KEY.")
    else:
        print(".env file already exists.")


def main():
    """Main installation function"""
    print("QA Agent Installation Script")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install system dependencies
    if not install_system_dependencies():
        print("Warning: System dependency installation failed")
    
    # Install Python dependencies
    if not install_python_dependencies():
        print("Error: Failed to install Python dependencies")
        sys.exit(1)
    
    # Create config directory
    create_config_directory()
    
    # Setup environment
    setup_environment()
    
    # Create desktop shortcut (optional)
    create_desktop_shortcut()
    
    print("\n" + "=" * 40)
    print("Installation completed!")
    print("\nNext steps:")
    print("1. Edit .env file and add your GEMINI_API_KEY")
    print("2. Run the application:")
    print("   python launch.py tray    # System tray mode")
    print("   python launch.py system  # Background service")
    print("   python launch.py api     # Web API mode")
    print("\nFor more information, see README.md")


if __name__ == "__main__":
    main()
