import pytest
import tempfile
import os
from pathlib import Path

from app.core.app_config import Config<PERSON><PERSON><PERSON>, AppConfig, HotkeyConfig
from app.db.session_manager import SessionManager
from app.utils.window_detector import WindowInfo


class TestConfiguration:
    """Test configuration management"""
    
    def test_default_config_creation(self):
        """Test that default configuration is created properly"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "test_config.yaml"
            manager = ConfigManager(config_path)
            
            config = manager.load_config()
            
            assert isinstance(config, AppConfig)
            assert "capture_question" in config.hotkeys
            assert "capture_solution" in config.hotkeys
            assert "analyze" in config.hotkeys
            
            # Check default hotkeys
            question_hotkey = config.hotkeys["capture_question"]
            assert question_hotkey.keys == ["ctrl", "shift", "q"]
            assert question_hotkey.action == "capture_question"
    
    def test_config_persistence(self):
        """Test that configuration is saved and loaded correctly"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "test_config.yaml"
            manager = ConfigManager(config_path)
            
            # Load and modify config
            config = manager.load_config()
            manager.update_hotkey("test_hotkey", ["ctrl", "alt", "t"], "Test hotkey")
            
            # Create new manager and load config
            new_manager = ConfigManager(config_path)
            new_config = new_manager.load_config()
            
            assert "test_hotkey" in new_config.hotkeys
            assert new_config.hotkeys["test_hotkey"].keys == ["ctrl", "alt", "t"]


class TestSessionManager:
    """Test session management"""
    
    def test_session_creation(self):
        """Test creating a new session"""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, "test.db")
            manager = SessionManager(f"sqlite:///{db_path}")
            
            window_info = WindowInfo(
                title="Test Window",
                app_name="Test App",
                process_name="test_process"
            )
            
            session = manager.create_session(window_info, "Test Session")
            
            assert session.id is not None
            assert session.session_name == "Test Session"
            assert session.app_name == "Test App"
            assert session.is_active is True
    
    def test_entry_creation(self):
        """Test creating Q&A entries"""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, "test.db")
            manager = SessionManager(f"sqlite:///{db_path}")
            
            # Create session first
            session = manager.create_session()
            
            # Create entry
            entry = manager.create_entry(session.id)
            
            assert entry.id is not None
            assert entry.session_id == session.id
    
    def test_question_capture(self):
        """Test capturing questions"""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, "test.db")
            manager = SessionManager(f"sqlite:///{db_path}")
            
            window_info = WindowInfo(
                title="Test Window",
                app_name="Test App",
                process_name="test_process"
            )
            
            question = "What is the best way to implement a binary search?"
            entry = manager.capture_question(question, window_info)
            
            assert entry.question == question
            assert entry.question_captured_at is not None
            assert entry.question_context is not None
    
    def test_solution_capture(self):
        """Test capturing solutions"""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, "test.db")
            manager = SessionManager(f"sqlite:///{db_path}")
            
            # First capture a question
            question = "How to sort an array?"
            manager.capture_question(question)
            
            # Then capture a solution
            solution = "Use the built-in sort() method"
            entry = manager.capture_solution(solution)
            
            assert entry.question == question
            assert entry.solution == solution
            assert entry.solution_captured_at is not None


class TestWindowDetector:
    """Test window detection functionality"""
    
    def test_window_info_creation(self):
        """Test WindowInfo dataclass"""
        window_info = WindowInfo(
            title="Test Window",
            app_name="Test App",
            process_name="test_process",
            pid=1234
        )
        
        assert window_info.title == "Test Window"
        assert window_info.app_name == "Test App"
        assert window_info.process_name == "test_process"
        assert window_info.pid == 1234
    
    def test_browser_detection(self):
        """Test browser window detection"""
        from app.utils.window_detector import WindowDetector
        
        detector = WindowDetector()
        
        # Test browser detection
        browser_window = WindowInfo(
            title="Google - Chrome",
            app_name="Google Chrome",
            process_name="chrome"
        )
        
        assert detector.is_browser_window(browser_window) is True
        
        # Test non-browser
        editor_window = WindowInfo(
            title="test.py - VSCode",
            app_name="Visual Studio Code",
            process_name="code"
        )
        
        assert detector.is_browser_window(editor_window) is False
    
    def test_code_editor_detection(self):
        """Test code editor detection"""
        from app.utils.window_detector import WindowDetector
        
        detector = WindowDetector()
        
        # Test code editor detection
        editor_window = WindowInfo(
            title="test.py - VSCode",
            app_name="Visual Studio Code",
            process_name="code"
        )
        
        assert detector.is_code_editor(editor_window) is True
        
        # Test non-editor
        browser_window = WindowInfo(
            title="Google - Chrome",
            app_name="Google Chrome",
            process_name="chrome"
        )
        
        assert detector.is_code_editor(browser_window) is False


class TestHotkeyConfig:
    """Test hotkey configuration"""
    
    def test_hotkey_config_creation(self):
        """Test creating hotkey configuration"""
        hotkey = HotkeyConfig(
            keys=["ctrl", "shift", "q"],
            action="capture_question",
            description="Capture question"
        )
        
        assert hotkey.keys == ["ctrl", "shift", "q"]
        assert hotkey.action == "capture_question"
        assert hotkey.description == "Capture question"
    
    def test_hotkey_validation(self):
        """Test hotkey validation"""
        # Valid hotkey
        hotkey = HotkeyConfig(
            keys=["ctrl", "q"],
            action="test_action"
        )
        
        assert len(hotkey.keys) == 2
        assert hotkey.action == "test_action"


if __name__ == "__main__":
    pytest.main([__file__])
