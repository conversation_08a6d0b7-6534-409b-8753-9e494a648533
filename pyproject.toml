[tool.poetry]
name = "agent"
version = "0.1.0"
description = ""
authors = ["kartikjn <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
langgraph = "^0.4.10"
langsmith = "^0.4.2"
fastapi = {extras = ["standard"], version = "^0.116.1"}
pydantic = "^2.11.7"
langchain = {extras = ["google-genai"], version = "^0.3.26"}
sqlalchemy = "^2.0.41"
pydantic-settings = "^2.10.1"
psycopg2-binary = "^2.9.10"
pytest = "^8.4.1"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
